<?php

namespace Tests\Unit\Model;

use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Booking::class)]
class BookingModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function booking_has_correct_fillable_attributes()
    {
        $booking = new Booking;

        $expectedFillable = [
            'field_id',
            'user_id',
            'booked_by',
            'booking_date',
            'start_time',
            'end_time',
            'duration_hours',
            'total_cost',
            'status',
            'customer_name',
            'customer_email',
            'customer_phone',
            'special_requests',
            'admin_notes',
            'confirmed_at',
            'cancelled_at',
        ];

        $this->assertEquals($expectedFillable, $booking->getFillable());
    }

    #[Test]
    public function booking_has_correct_casts()
    {
        $booking = new Booking;

        $expectedCasts = [
            'id' => 'int',
            'booking_date' => 'date',
            'total_cost' => 'decimal:2',
            'confirmed_at' => 'datetime',
            'cancelled_at' => 'datetime',
        ];

        $this->assertEquals($expectedCasts, $booking->getCasts());
    }

    #[Test]
    public function booking_uses_has_factory_trait()
    {
        $this->assertTrue(method_exists(Booking::class, 'factory'));

        $booking = Booking::factory()->create();
        $this->assertInstanceOf(Booking::class, $booking);
        $this->assertNotNull($booking->id);
    }

    #[Test]
    public function booking_has_correct_table_name()
    {
        $booking = new Booking;
        $this->assertEquals('bookings', $booking->getTable());
    }

    #[Test]
    public function booking_has_correct_primary_key()
    {
        $booking = new Booking;
        $this->assertEquals('id', $booking->getKeyName());
        $this->assertTrue($booking->getIncrementing());
        $this->assertEquals('int', $booking->getKeyType());
    }

    #[Test]
    public function booking_has_timestamps()
    {
        $booking = Booking::factory()->create();

        $this->assertNotNull($booking->created_at);
        $this->assertNotNull($booking->updated_at);
        $this->assertInstanceOf(Carbon::class, $booking->created_at);
        $this->assertInstanceOf(Carbon::class, $booking->updated_at);
    }

    #[Test]
    public function field_relationship_returns_belongs_to()
    {
        $booking = Booking::factory()->create();

        $relationship = $booking->field();

        $this->assertInstanceOf(BelongsTo::class, $relationship);
        $this->assertEquals('field_id', $relationship->getForeignKeyName());
        $this->assertEquals('id', $relationship->getOwnerKeyName());
    }

    #[Test]
    public function user_relationship_returns_belongs_to()
    {
        $booking = Booking::factory()->create();

        $relationship = $booking->user();

        $this->assertInstanceOf(BelongsTo::class, $relationship);
        $this->assertEquals('user_id', $relationship->getForeignKeyName());
        $this->assertEquals('id', $relationship->getOwnerKeyName());
    }

    #[Test]
    public function booked_by_relationship_returns_belongs_to()
    {
        $booking = Booking::factory()->create();

        $relationship = $booking->bookedBy();

        $this->assertInstanceOf(BelongsTo::class, $relationship);
        $this->assertEquals('booked_by', $relationship->getForeignKeyName());
        $this->assertEquals('id', $relationship->getOwnerKeyName());
    }

    #[Test]
    public function booking_belongs_to_field()
    {
        $field = Field::factory()->create(['name' => 'Test Field']);
        $booking = Booking::factory()->create(['field_id' => $field->id]);

        $this->assertInstanceOf(Field::class, $booking->field);
        $this->assertEquals($field->id, $booking->field->id);
        $this->assertEquals('Test Field', $booking->field->name);
    }

    #[Test]
    public function booking_belongs_to_user()
    {
        $user = User::factory()->create(['name' => 'Test User']);
        $booking = Booking::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $booking->user);
        $this->assertEquals($user->id, $booking->user->id);
        $this->assertEquals('Test User', $booking->user->name);
    }

    #[Test]
    public function booking_belongs_to_admin_user_who_booked_it()
    {
        $admin = User::factory()->create(['name' => 'Admin User', 'role' => 'admin']);
        $booking = Booking::factory()->create(['booked_by' => $admin->id]);

        $this->assertInstanceOf(User::class, $booking->bookedBy);
        $this->assertEquals($admin->id, $booking->bookedBy->id);
        $this->assertEquals('Admin User', $booking->bookedBy->name);
    }

    #[Test]
    public function booked_by_relationship_can_be_null()
    {
        $booking = Booking::factory()->create(['booked_by' => null]);

        $this->assertNull($booking->bookedBy);
    }

    #[Test]
    public function get_statuses_returns_correct_array()
    {
        $statuses = Booking::getStatuses();

        $expectedStatuses = [
            'Pending' => 'Pending',
            'Confirmed' => 'Confirmed',
            'Cancelled' => 'Cancelled',
            'Completed' => 'Completed',
        ];

        $this->assertIsArray($statuses);
        $this->assertEquals($expectedStatuses, $statuses);
    }

    #[Test]
    public function calculate_total_cost_returns_correct_amount()
    {
        $field = Field::factory()->create(['hourly_rate' => 50.00]);
        $booking = Booking::factory()->create([
            'field_id' => $field->id,
            'duration_hours' => 3,
        ]);

        $totalCost = $booking->calculateTotalCost();

        $this->assertEquals(150.00, $totalCost);
    }

    #[Test]
    public function get_time_range_attribute_returns_formatted_time()
    {
        $booking = Booking::factory()->create([
            'start_time' => '14:30',
            'end_time' => '16:30',
        ]);

        $timeRange = $booking->time_range;

        $this->assertEquals('14:30 - 16:30', $timeRange);
    }

    #[Test]
    public function get_formatted_date_time_attribute_returns_formatted_string()
    {
        $booking = Booking::factory()->create([
            'booking_date' => '2024-01-15',
            'start_time' => '14:30',
            'end_time' => '16:30',
        ]);

        $formattedDateTime = $booking->formatted_date_time;

        $this->assertEquals('Jan 15, 2024 at 14:30 - 16:30', $formattedDateTime);
    }

    #[Test]
    public function get_customer_display_name_returns_customer_name_when_available()
    {
        $booking = Booking::factory()->create([
            'customer_name' => 'John Doe',
        ]);

        $displayName = $booking->customer_display_name;

        $this->assertEquals('John Doe', $displayName);
    }

    #[Test]
    public function get_customer_display_name_returns_user_name_when_no_customer_name()
    {
        $user = User::factory()->create(['name' => 'Jane Smith']);
        $booking = Booking::factory()->create([
            'user_id' => $user->id,
            'customer_name' => null,
        ]);

        $displayName = $booking->customer_display_name;

        $this->assertEquals('Jane Smith', $displayName);
    }

    #[Test]
    public function get_customer_display_name_returns_unknown_when_no_user_or_customer_name()
    {
        // Create a user first, then manually set the relationship to null
        $user = User::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $user->id,
            'customer_name' => null,
        ]);

        // Manually unset the user relationship to simulate null user
        $booking->setRelation('user', null);

        $displayName = $booking->customer_display_name;

        $this->assertEquals('Unknown', $displayName);
    }

    #[Test]
    public function get_status_color_attribute_returns_correct_colors()
    {
        $pendingBooking = Booking::factory()->create(['status' => 'Pending']);
        $confirmedBooking = Booking::factory()->create(['status' => 'Confirmed']);
        $cancelledBooking = Booking::factory()->create(['status' => 'Cancelled']);
        $completedBooking = Booking::factory()->create(['status' => 'Completed']);

        $this->assertEquals('yellow', $pendingBooking->status_color);
        $this->assertEquals('lightblue', $confirmedBooking->status_color);
        $this->assertEquals('red', $cancelledBooking->status_color);
        $this->assertEquals('blue', $completedBooking->status_color);
    }

    #[Test]
    public function get_status_color_attribute_returns_gray_for_unknown_status()
    {
        $booking = Booking::factory()->create();
        $booking->status = 'Unknown Status';

        $statusColor = $booking->status_color;

        $this->assertEquals('gray', $statusColor);
    }

    #[Test]
    public function can_be_cancelled_returns_true_for_pending_future_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Pending',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertTrue($booking->canBeCancelled());
    }

    #[Test]
    public function can_be_cancelled_returns_true_for_confirmed_future_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertTrue($booking->canBeCancelled());
    }

    #[Test]
    public function can_be_cancelled_returns_false_for_cancelled_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Cancelled',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertFalse($booking->canBeCancelled());
    }

    #[Test]
    public function can_be_cancelled_returns_false_for_completed_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Completed',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertFalse($booking->canBeCancelled());
    }

    #[Test]
    public function can_be_cancelled_returns_false_for_past_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Pending',
            'booking_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $this->assertFalse($booking->canBeCancelled());
    }

    #[Test]
    public function can_be_confirmed_returns_true_for_pending_future_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Pending',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertTrue($booking->canBeConfirmed());
    }

    #[Test]
    public function can_be_confirmed_returns_false_for_confirmed_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertFalse($booking->canBeConfirmed());
    }

    #[Test]
    public function can_be_confirmed_returns_false_for_cancelled_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Cancelled',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $this->assertFalse($booking->canBeConfirmed());
    }

    #[Test]
    public function can_be_confirmed_returns_false_for_past_booking()
    {
        $booking = Booking::factory()->create([
            'status' => 'Pending',
            'booking_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $this->assertFalse($booking->canBeConfirmed());
    }

    #[Test]
    public function upcoming_scope_returns_future_bookings()
    {
        $futureBooking = Booking::factory()->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);
        $todayBooking = Booking::factory()->create([
            'booking_date' => now()->format('Y-m-d'),
        ]);
        $pastBooking = Booking::factory()->create([
            'booking_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $upcomingBookings = Booking::upcoming()->get();

        $this->assertTrue($upcomingBookings->contains($futureBooking));
        $this->assertTrue($upcomingBookings->contains($todayBooking));
        $this->assertFalse($upcomingBookings->contains($pastBooking));
    }

    #[Test]
    public function past_scope_returns_past_bookings()
    {
        $futureBooking = Booking::factory()->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);
        $todayBooking = Booking::factory()->create([
            'booking_date' => now()->format('Y-m-d'),
        ]);
        $pastBooking = Booking::factory()->create([
            'booking_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $pastBookings = Booking::past()->get();

        $this->assertFalse($pastBookings->contains($futureBooking));
        $this->assertFalse($pastBookings->contains($todayBooking));
        $this->assertTrue($pastBookings->contains($pastBooking));
    }

    #[Test]
    public function active_scope_returns_pending_and_confirmed_bookings()
    {
        $pendingBooking = Booking::factory()->create(['status' => 'Pending']);
        $confirmedBooking = Booking::factory()->create(['status' => 'Confirmed']);
        $cancelledBooking = Booking::factory()->create(['status' => 'Cancelled']);
        $completedBooking = Booking::factory()->create(['status' => 'Completed']);

        $activeBookings = Booking::active()->get();

        $this->assertTrue($activeBookings->contains($pendingBooking));
        $this->assertTrue($activeBookings->contains($confirmedBooking));
        $this->assertFalse($activeBookings->contains($cancelledBooking));
        $this->assertFalse($activeBookings->contains($completedBooking));
    }

    #[Test]
    public function by_status_scope_returns_bookings_with_specific_status()
    {
        $pendingBooking = Booking::factory()->create(['status' => 'Pending']);
        $confirmedBooking = Booking::factory()->create(['status' => 'Confirmed']);
        $cancelledBooking = Booking::factory()->create(['status' => 'Cancelled']);

        $pendingBookings = Booking::byStatus('Pending')->get();
        $confirmedBookings = Booking::byStatus('Confirmed')->get();

        $this->assertTrue($pendingBookings->contains($pendingBooking));
        $this->assertFalse($pendingBookings->contains($confirmedBooking));
        $this->assertFalse($pendingBookings->contains($cancelledBooking));

        $this->assertTrue($confirmedBookings->contains($confirmedBooking));
        $this->assertFalse($confirmedBookings->contains($pendingBooking));
        $this->assertFalse($confirmedBookings->contains($cancelledBooking));
    }

    #[Test]
    public function on_date_scope_returns_bookings_on_specific_date()
    {
        $targetDate = '2024-01-15';
        $bookingOnDate = Booking::factory()->create(['booking_date' => $targetDate]);
        $bookingOnOtherDate = Booking::factory()->create(['booking_date' => '2024-01-16']);

        $bookingsOnDate = Booking::onDate($targetDate)->get();

        $this->assertTrue($bookingsOnDate->contains($bookingOnDate));
        $this->assertFalse($bookingsOnDate->contains($bookingOnOtherDate));
    }

    #[Test]
    public function booking_mass_assignment_protection_works()
    {
        $booking = new Booking;

        // Test that only fillable attributes can be mass assigned
        $booking->fill([
            'field_id' => 1,
            'user_id' => 1,
            'booking_date' => '2024-01-15',
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'total_cost' => 100.00,
            'status' => 'Pending',
            'id' => 999, // Should be ignored
            'created_at' => now(), // Should be ignored
        ]);

        $this->assertEquals(1, $booking->field_id);
        $this->assertEquals(1, $booking->user_id);
        $this->assertEquals('2024-01-15', $booking->booking_date->format('Y-m-d'));
        $this->assertEquals('10:00', $booking->start_time);
        $this->assertEquals('12:00', $booking->end_time);
        $this->assertEquals(2, $booking->duration_hours);
        $this->assertEquals(100.00, $booking->total_cost);
        $this->assertEquals('Pending', $booking->status);
        $this->assertNull($booking->id); // Should not be set
        $this->assertNull($booking->created_at); // Should not be set
    }

    #[Test]
    public function booking_can_be_serialized_to_array()
    {
        $booking = Booking::factory()->create([
            'booking_date' => '2024-01-15',
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
            'total_cost' => 150.75,
        ]);

        $array = $booking->toArray();

        $this->assertIsArray($array);
        $this->assertEquals('2024-01-15T00:00:00.000000Z', $array['booking_date']);
        $this->assertEquals('10:00', $array['start_time']);
        $this->assertEquals('12:00', $array['end_time']);
        $this->assertEquals('Confirmed', $array['status']);
        $this->assertEquals('150.75', $array['total_cost']);
        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    #[Test]
    public function booking_can_be_serialized_to_json()
    {
        $booking = Booking::factory()->create([
            'booking_date' => '2024-01-15',
            'status' => 'Pending',
        ]);

        $json = $booking->toJson();
        $decoded = json_decode($json, true);

        $this->assertIsString($json);
        $this->assertIsArray($decoded);
        $this->assertEquals('2024-01-15T00:00:00.000000Z', $decoded['booking_date']);
        $this->assertEquals('Pending', $decoded['status']);
    }

    #[Test]
    public function booking_date_casting_works_correctly()
    {
        $booking = Booking::factory()->create([
            'booking_date' => '2024-01-15',
        ]);

        $this->assertInstanceOf(Carbon::class, $booking->booking_date);
        $this->assertEquals('2024-01-15', $booking->booking_date->format('Y-m-d'));
    }

    #[Test]
    public function total_cost_casting_works_correctly()
    {
        $booking = Booking::factory()->create([
            'total_cost' => 150.5,
        ]);

        // Test that decimal casting returns string with 2 decimal places
        $this->assertIsString($booking->total_cost);
        $this->assertEquals('150.50', $booking->total_cost);

        // Test that raw value is still numeric
        $this->assertIsNumeric($booking->getRawOriginal('total_cost'));
    }

    #[Test]
    public function confirmed_at_casting_works_correctly()
    {
        $confirmedAt = now();
        $booking = Booking::factory()->create([
            'confirmed_at' => $confirmedAt,
        ]);

        $this->assertInstanceOf(Carbon::class, $booking->confirmed_at);
        $this->assertEquals($confirmedAt->format('Y-m-d H:i:s'), $booking->confirmed_at->format('Y-m-d H:i:s'));
    }

    #[Test]
    public function cancelled_at_casting_works_correctly()
    {
        $cancelledAt = now();
        $booking = Booking::factory()->create([
            'cancelled_at' => $cancelledAt,
        ]);

        $this->assertInstanceOf(Carbon::class, $booking->cancelled_at);
        $this->assertEquals($cancelledAt->format('Y-m-d H:i:s'), $booking->cancelled_at->format('Y-m-d H:i:s'));
    }

    #[Test]
    public function booking_factory_creates_valid_booking()
    {
        $booking = Booking::factory()->create([
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
        ]);

        $this->assertInstanceOf(Booking::class, $booking);
        $this->assertNotNull($booking->field_id);
        $this->assertNotNull($booking->user_id);
        $this->assertNotNull($booking->booking_date);
        $this->assertNotNull($booking->start_time);
        $this->assertNotNull($booking->end_time);
        $this->assertNotNull($booking->duration_hours);
        $this->assertNotNull($booking->total_cost);
        $this->assertNotNull($booking->status);
        $this->assertIsInt($booking->duration_hours);
        $this->assertIsString($booking->total_cost);
        $this->assertContains($booking->status, ['Pending', 'Confirmed', 'Cancelled', 'Completed']);
        $this->assertGreaterThan(0, $booking->duration_hours);
    }

    #[Test]
    public function booking_factory_states_work_correctly()
    {
        $pendingBooking = Booking::factory()->pending()->create();
        $this->assertEquals('Pending', $pendingBooking->status);
        $this->assertNull($pendingBooking->confirmed_at);
        $this->assertNull($pendingBooking->cancelled_at);

        $confirmedBooking = Booking::factory()->confirmed()->create();
        $this->assertEquals('Confirmed', $confirmedBooking->status);
        $this->assertNotNull($confirmedBooking->confirmed_at);
        $this->assertNull($confirmedBooking->cancelled_at);

        $cancelledBooking = Booking::factory()->cancelled()->create();
        $this->assertEquals('Cancelled', $cancelledBooking->status);
        $this->assertNull($cancelledBooking->confirmed_at);
        $this->assertNotNull($cancelledBooking->cancelled_at);

        $completedBooking = Booking::factory()->completed()->create();
        $this->assertEquals('Completed', $completedBooking->status);
        $this->assertNotNull($completedBooking->confirmed_at);
        $this->assertNull($completedBooking->cancelled_at);
        $this->assertTrue($completedBooking->booking_date->isPast());
    }

    #[Test]
    public function booking_factory_date_states_work_correctly()
    {
        $todayBooking = Booking::factory()->today()->create();
        $this->assertEquals(now()->format('Y-m-d'), $todayBooking->booking_date->format('Y-m-d'));

        $tomorrowBooking = Booking::factory()->tomorrow()->create();
        $this->assertEquals(now()->addDay()->format('Y-m-d'), $tomorrowBooking->booking_date->format('Y-m-d'));

        $upcomingBooking = Booking::factory()->upcoming()->create();
        $this->assertTrue($upcomingBooking->booking_date->isFuture() || $upcomingBooking->booking_date->isToday());

        $pastBooking = Booking::factory()->past()->create();
        $this->assertTrue($pastBooking->booking_date->isPast());
    }

    #[Test]
    public function booking_factory_admin_booked_state_works()
    {
        $adminBookedBooking = Booking::factory()->adminBooked()->create();

        $this->assertNotNull($adminBookedBooking->booked_by);
        $this->assertInstanceOf(User::class, $adminBookedBooking->bookedBy);
        $this->assertEquals('admin', $adminBookedBooking->bookedBy->role);
    }

    #[Test]
    public function booking_can_be_created_with_all_fillable_attributes()
    {
        $field = Field::factory()->create();
        $user = User::factory()->create();
        $admin = User::factory()->create(['role' => 'admin']);

        $bookingData = [
            'field_id' => $field->id,
            'user_id' => $user->id,
            'booked_by' => $admin->id,
            'booking_date' => '2024-01-15',
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'total_cost' => 100.50,
            'status' => 'Confirmed',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+1234567890',
            'special_requests' => 'Please provide equipment',
            'admin_notes' => 'VIP customer',
            'confirmed_at' => now(),
            'cancelled_at' => null,
        ];

        $booking = Booking::create($bookingData);

        $this->assertInstanceOf(Booking::class, $booking);
        $this->assertEquals($field->id, $booking->field_id);
        $this->assertEquals($user->id, $booking->user_id);
        $this->assertEquals($admin->id, $booking->booked_by);
        $this->assertEquals('2024-01-15', $booking->booking_date->format('Y-m-d'));
        $this->assertEquals('10:00', $booking->start_time);
        $this->assertEquals('12:00', $booking->end_time);
        $this->assertEquals(2, $booking->duration_hours);
        $this->assertEquals('100.50', $booking->total_cost);
        $this->assertEquals('Confirmed', $booking->status);
        $this->assertEquals('John Doe', $booking->customer_name);
        $this->assertEquals('<EMAIL>', $booking->customer_email);
        $this->assertEquals('+1234567890', $booking->customer_phone);
        $this->assertEquals('Please provide equipment', $booking->special_requests);
        $this->assertEquals('VIP customer', $booking->admin_notes);
        $this->assertNotNull($booking->confirmed_at);
        $this->assertNull($booking->cancelled_at);
    }

    #[Test]
    public function booking_model_has_correct_database_connection()
    {
        $booking = new Booking;

        // Should use default connection
        $this->assertNull($booking->getConnectionName());
    }

    #[Test]
    public function booking_model_uses_correct_date_format()
    {
        $booking = Booking::factory()->create();

        // Test that timestamps are properly formatted
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $booking->created_at->toDateTimeString());
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $booking->updated_at->toDateTimeString());
    }

    #[Test]
    public function booking_model_handles_null_values_correctly()
    {
        $booking = Booking::factory()->create([
            'booked_by' => null,
            'customer_name' => null,
            'customer_email' => null,
            'customer_phone' => null,
            'special_requests' => null,
            'admin_notes' => null,
            'confirmed_at' => null,
            'cancelled_at' => null,
        ]);

        $this->assertNull($booking->booked_by);
        $this->assertNull($booking->customer_name);
        $this->assertNull($booking->customer_email);
        $this->assertNull($booking->customer_phone);
        $this->assertNull($booking->special_requests);
        $this->assertNull($booking->admin_notes);
        $this->assertNull($booking->confirmed_at);
        $this->assertNull($booking->cancelled_at);
    }

    #[Test]
    public function booking_relationships_can_be_eager_loaded()
    {
        $field = Field::factory()->create(['name' => 'Test Field']);
        $user = User::factory()->create(['name' => 'Test User']);
        $admin = User::factory()->create(['name' => 'Admin User', 'role' => 'admin']);

        $booking = Booking::factory()->create([
            'field_id' => $field->id,
            'user_id' => $user->id,
            'booked_by' => $admin->id,
        ]);

        $loadedBooking = Booking::with(['field', 'user', 'bookedBy'])->find($booking->id);

        $this->assertTrue($loadedBooking->relationLoaded('field'));
        $this->assertTrue($loadedBooking->relationLoaded('user'));
        $this->assertTrue($loadedBooking->relationLoaded('bookedBy'));
        $this->assertEquals('Test Field', $loadedBooking->field->name);
        $this->assertEquals('Test User', $loadedBooking->user->name);
        $this->assertEquals('Admin User', $loadedBooking->bookedBy->name);
    }

    #[Test]
    public function booking_scopes_can_be_chained()
    {
        $futureConfirmedBooking = Booking::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        $pastConfirmedBooking = Booking::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $futurePendingBooking = Booking::factory()->create([
            'status' => 'Pending',
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        // Chain upcoming and byStatus scopes
        $upcomingConfirmedBookings = Booking::upcoming()->byStatus('Confirmed')->get();

        $this->assertTrue($upcomingConfirmedBookings->contains($futureConfirmedBooking));
        $this->assertFalse($upcomingConfirmedBookings->contains($pastConfirmedBooking));
        $this->assertFalse($upcomingConfirmedBookings->contains($futurePendingBooking));
    }

    #[Test]
    public function booking_time_range_handles_different_time_formats()
    {
        $booking1 = Booking::factory()->create([
            'start_time' => '09:00',
            'end_time' => '11:00',
        ]);

        $booking2 = Booking::factory()->create([
            'start_time' => '14:30',
            'end_time' => '16:45',
        ]);

        $this->assertEquals('09:00 - 11:00', $booking1->time_range);
        $this->assertEquals('14:30 - 16:45', $booking2->time_range);
    }

    #[Test]
    public function calculate_total_cost_handles_decimal_rates()
    {
        $field = Field::factory()->create(['hourly_rate' => 75.25]);
        $booking = Booking::factory()->create([
            'field_id' => $field->id,
            'duration_hours' => 3,
        ]);

        $totalCost = $booking->calculateTotalCost();

        $this->assertEquals(225.75, $totalCost);
    }

    #[Test]
    public function booking_status_validation_with_all_valid_statuses()
    {
        $validStatuses = ['Pending', 'Confirmed', 'Cancelled', 'Completed'];

        foreach ($validStatuses as $status) {
            $booking = Booking::factory()->create(['status' => $status]);
            $this->assertEquals($status, $booking->status);
        }
    }

    #[Test]
    public function booking_date_comparison_methods_work_correctly()
    {
        $futureBooking = Booking::factory()->create([
            'booking_date' => now()->addDays(5)->format('Y-m-d'),
        ]);

        $pastBooking = Booking::factory()->create([
            'booking_date' => now()->subDays(5)->format('Y-m-d'),
        ]);

        $this->assertTrue($futureBooking->booking_date->isFuture());
        $this->assertFalse($futureBooking->booking_date->isPast());

        $this->assertTrue($pastBooking->booking_date->isPast());
        $this->assertFalse($pastBooking->booking_date->isFuture());
    }
}
