<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Booking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'field_id',
        'user_id',
        'booked_by',
        'booking_date',
        'start_time',
        'end_time',
        'duration_hours',
        'total_cost',
        'status',
        'customer_name',
        'customer_email',
        'customer_phone',
        'special_requests',
        'admin_notes',
        'confirmed_at',
        'cancelled_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'booking_date' => 'date',
        'total_cost' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Available booking statuses
     */
    public static function getStatuses(): array
    {
        return [
            'Pending' => 'Pending',
            'Confirmed' => 'Confirmed',
            'Cancelled' => 'Cancelled',
            'Completed' => 'Completed',
        ];
    }

    /**
     * Get the field that owns the booking
     */
    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the user that owns the booking
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin user who made the booking
     */
    public function bookedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'booked_by');
    }

    /**
     * Calculate total cost based on field hourly rate and duration
     */
    public function calculateTotalCost(): float
    {
        return $this->field->hourly_rate * $this->duration_hours;
    }

    /**
     * Get formatted booking time range
     */
    public function getTimeRangeAttribute(): string
    {
        return Carbon::parse($this->start_time)->format('H:i').' - '.Carbon::parse($this->end_time)->format('H:i');
    }

    /**
     * Get formatted booking date and time
     */
    public function getFormattedDateTimeAttribute(): string
    {
        return $this->booking_date->format('M d, Y').' at '.$this->time_range;
    }

    /**
     * Get customer display name
     */
    public function getCustomerDisplayNameAttribute(): string
    {
        if ($this->customer_name) {
            return $this->customer_name;
        }

        return $this->user->name ?? 'Unknown';
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'Pending' => 'yellow',
            'Confirmed' => 'lightblue',
            'Cancelled' => 'red',
            'Completed' => 'blue',
            default => 'gray',
        };
    }

    /**
     * Check if booking can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['Pending', 'Confirmed']) &&
               $this->booking_date->isFuture();
    }

    /**
     * Check if booking can be confirmed
     */
    public function canBeConfirmed(): bool
    {
        return $this->status === 'Pending' &&
               $this->booking_date->isFuture();
    }

    /**
     * Scope for upcoming bookings
     */
    public function scopeUpcoming($query)
    {
        return $query->whereDate('booking_date', '>=', now()->toDateString());
    }

    /**
     * Scope for past bookings
     */
    public function scopePast($query)
    {
        return $query->whereDate('booking_date', '<', now()->toDateString());
    }

    /**
     * Scope for active bookings (pending or confirmed)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['Pending', 'Confirmed']);
    }

    /**
     * Scope for bookings by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for bookings on a specific date
     */
    public function scopeOnDate($query, string $date)
    {
        return $query->whereDate('booking_date', $date);
    }
}
