@extends('layouts.admin')

@section('title', 'Reservations Calendar - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Calendar</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Calendar</li>
                </ol>
            </nav>
        </div>
    </div>

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Calendar Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations Calendar</div>
                    <div class="d-flex gap-2 align-items-center">
                        <!-- Field Filter -->
                        <select id="fieldFilter" class="form-select form-select-sm">
                            <option value="">All Fields</option>
                            @foreach ($fields as $field)
                                <option value="{{ $field->id }}">{{ $field->name }}</option>
                            @endforeach
                        </select>

                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Legend -->
                    <div class="mb-4 p-3 bg-light rounded">
                        <h6 class="fw-semibold mb-2">Legend:</h6>
                        <div class="d-flex flex-wrap gap-3 fs-12">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-warning me-2">&nbsp;</span>
                                <span>Pending</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">&nbsp;</span>
                                <span>Confirmed</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">&nbsp;</span>
                                <span>Completed</span>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="calendarLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading calendar...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading calendar events...</p>
                    </div>

                    <!-- Calendar Container -->
                    <div id="calendar" style="display: none;"></div>

                    <!-- Error State -->
                    <div id="calendarError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Calendar Error</h6>
                        <p class="mb-0">Failed to load calendar events. Please refresh the page or contact support.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FullCalendar CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const fieldFilter = document.getElementById('fieldFilter');

            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                timeZone: 'local',
                eventDisplay: 'block',
                displayEventTime: true,
                weekNumbers: true,
                weekNumberCalculation: 'ISO',
                navLinks: true,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                editable: true,
                selectable: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    const fieldId = fieldFilter.value;
                    const url = new URL('{{ route('calendar.events') }}');
                    url.searchParams.append('start', fetchInfo.startStr);
                    url.searchParams.append('end', fetchInfo.endStr);
                    if (fieldId) {
                        url.searchParams.append('field_id', fieldId);
                    }

                    console.log('Fetching calendar events from:', url.toString());

                    fetch(url)
                        .then(response => {
                            console.log('Calendar events response status:',
                                response.status);
                            if (!response.ok) {
                                throw new Error(
                                    `HTTP error! status: ${response.status}`
                                );
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events data received:', data);
                            document.getElementById('calendarLoading').style
                                .display = 'none';
                            document.getElementById('calendar').style.display =
                                'block';
                            document.getElementById('calendarError').classList
                                .add('d-none');
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Calendar events fetch error:',
                                error);
                            document.getElementById('calendarLoading').style
                                .display = 'none';
                            document.getElementById('calendarError').classList
                                .remove('d-none');
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    // Prevent default navigation
                    info.jsEvent.preventDefault();

                    // Open reservation details in same window
                    window.location.href = info.event.url;
                },
                dateClick: function(info) {
                    // Redirect to a specific page with the date as a query parameter
                    let url = '/reservations/create?date=' + encodeURIComponent(info.dateStr);
    
                    // If time is included (time grid views), also pass time separately
                    if (info.dateStr.includes('T')) {
                        const [date, time] = info.dateStr.split('T');
                        url = `/reservations/create?date=${encodeURIComponent(date)}&time=${encodeURIComponent(time)}`;
                    }
                    
                    window.location.href = url;
                },
                eventDidMount: function(info) {
                    // Add tooltip with reservation details
                    const props = info.event.extendedProps;
                    info.el.title =
                        `${props.field_name}\nCustomer: ${props.customer_name}\nStatus: ${props.status}\nCost: $${props.total_cost}\nDuration: ${props.duration} hours`;
                },
                eventDrop: function(info) {
                    const event = info.event;

                    fetch(`/calendar/update-reservation/${event.id}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content')
                            },
                            body: JSON.stringify({
                                start: event.start.toISOString(),
                                end: event.end ? event.end.toISOString() : null
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to update reservation.');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log(data.message);
                        })
                        .catch(error => {
                            alert('Error updating reservation.');
                            info.revert(); // undo the drag
                        });
                }
            });

            // Show loading state initially
            document.getElementById('calendarLoading').style.display = 'block';
            document.getElementById(
                'calendar').style.display = 'none';

            calendar.render();

            // Initial load complete
            setTimeout(() => {
                document.getElementById('calendarLoading').style.display = 'none';
                document.getElementById('calendar').style.display = 'block';
            }, 1000);

            // Refresh calendar when field filter changes
            fieldFilter.addEventListener('change', function() {
                document.getElementById('calendarLoading').style.display = 'block';
                document.getElementById('calendar').style.display = 'none';
                calendar.refetchEvents();
            });
        });
    </script>
@endsection
